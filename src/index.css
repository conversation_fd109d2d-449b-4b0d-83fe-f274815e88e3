@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables matching the original */
:root {
  --bg: #f6f7fb;
  --card: #ffffff;
  --ink: #1f2937;
  --muted: #6b7280;
  --accent: #2563eb;
  --tile: #eef0f3;
  --cp: #f5e5a3;
  --det: #f3b0a3;
  --center: #e6e1d6;
  --line: #c9cfd8;
  --danger: #ef4444;
}

/* Base styles */
body {
  margin: 0;
  background: var(--bg);
  font-family: Inter, system-ui, -apple-system, 'Segoe UI', Roboto, Arial, sans-serif;
  color: var(--ink);
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* Mobile-first responsive layout */
.mobile-game-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--card);
  border-bottom: 1px solid var(--line);
  min-height: 60px;
}

.mobile-title h1 {
  font-size: 1.5rem;
  font-weight: 800;
  margin: 0;
  color: var(--ink);
}

.mobile-scores {
  display: flex;
  gap: 12px;
  margin-top: 4px;
}

.mobile-score {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.9rem;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.mobile-score.active-player {
  background: rgba(37, 99, 235, 0.1);
  border: 2px solid var(--accent);
  transform: scale(1.05);
}

.mobile-status {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--accent);
  text-align: right;
  max-width: 200px;
  line-height: 1.2;
}

.mobile-main-layout {
  flex: 1;
  display: flex;
  gap: 8px;
  padding: 8px;
  overflow: hidden;
}

.mobile-board-container {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
}

.mobile-board {
  width: 100%;
  height: 100%;
  max-height: calc(100vh - 140px);
  padding: 8px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-grid {
  display: grid;
  gap: 1px;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
}

.mobile-controls-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  max-width: 250px;
}

/* Mobile Controls */
.mobile-primary-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-button {
  border: 0;
  border-radius: 12px;
  padding: 12px 16px;
  background: #e5e7eb;
  color: #111827;
  font-weight: 700;
  font-size: 0.9rem;
  cursor: pointer;
  touch-action: manipulation;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  transition: all 0.2s ease;
}

.mobile-button:active {
  transform: scale(0.95);
  background: #d1d5db;
}

.mobile-button.primary {
  background: var(--accent);
  color: #fff;
}

.mobile-button.primary:active {
  background: #1d4ed8;
}

.mobile-button.active {
  background: var(--accent);
  color: #fff;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.3);
}

.mobile-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
  background: #f3f4f6 !important;
  color: #9ca3af !important;
}

.mobile-button-small {
  border: 0;
  border-radius: 8px;
  padding: 6px 10px;
  background: #f3f4f6;
  color: #111827;
  font-weight: 600;
  font-size: 0.75rem;
  cursor: pointer;
  touch-action: manipulation;
  min-height: 36px;
}

.mobile-button-small:active {
  transform: scale(0.95);
  background: #e5e7eb;
}

.mobile-legend {
  background: var(--card);
  border: 1px solid var(--line);
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 8px;
  font-size: 0.7rem;
}

.legend-title {
  font-weight: 700;
  color: var(--accent);
  margin-bottom: 4px;
}

.legend-items div {
  margin-bottom: 2px;
  color: var(--muted);
  line-height: 1.3;
}

.mobile-secondary-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: auto;
}

/* Mobile Dice and Pairs */
.mobile-dice-section {
  background: var(--card);
  border: 1px solid var(--line);
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 8px;
}

.mobile-dice-container {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-bottom: 12px;
}

.mobile-die {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: #111827;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 800;
  font-size: 1.1rem;
}

.mobile-pairs-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.mobile-pair {
  border: 2px solid var(--line);
  border-radius: 10px;
  padding: 8px 12px;
  background: #fff;
  cursor: pointer;
  touch-action: manipulation;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 44px;
}

.mobile-pair:active {
  transform: scale(0.98);
}

.mobile-pair.selected {
  border-color: var(--accent);
  background: rgba(37, 99, 235, 0.1);
  box-shadow: 0 0 0 1px rgba(37, 99, 235, 0.2);
}

.pair-sum {
  font-size: 1.2rem;
  font-weight: 800;
  color: var(--accent);
}

.pair-calc {
  font-size: 0.8rem;
  color: var(--muted);
}

/* Mobile Grid Cells */
.mobile-cell {
  aspect-ratio: 1;
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.6rem;
  min-height: 18px;
  min-width: 18px;
}

.mobile-label {
  font-weight: 700;
  color: var(--muted);
  background: transparent;
}

.mobile-step-number {
  position: absolute;
  bottom: 1px;
  right: 1px;
  font-size: 0.5rem;
  color: var(--muted);
  line-height: 1;
}

.mobile-carry-indicator {
  position: absolute;
  bottom: -2px;
  left: 0;
  font-size: 0.5rem;
  line-height: 1;
}

/* Custom component styles */
.swoop-tile {
  background: var(--tile);
  border: 1px solid #d7dbe1;
}

.swoop-cp {
  background: var(--cp) !important;
}

.swoop-det {
  background: var(--det) !important;
}

.swoop-center {
  background: var(--center) !important;
  border-style: dashed;
}

.swoop-highlight {
  outline: 3px dashed #94a3b8;
  outline-offset: 1px;
  cursor: pointer;
}

/* Mobile-optimized pieces */
.swoop-piece {
  position: relative;
  font-size: 1rem;
  line-height: 1;
}

.swoop-piece.active {
  font-size: 1.2rem;
  filter: drop-shadow(0 0 3px rgba(37, 99, 235, 0.6));
}

.swoop-piece.carry::after {
  content: '🧺';
  position: absolute;
  right: -4px;
  top: -8px;
  font-size: 0.7rem;
}

.swoop-ring {
  position: absolute;
  inset: -1px;
  border-radius: 6px;
  border: 2px solid var(--accent);
  pointer-events: none;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Mobile touch targets */
.swoop-highlight {
  outline: 2px dashed var(--accent);
  outline-offset: 1px;
  cursor: pointer;
  background: rgba(37, 99, 235, 0.15) !important;
  transform: scale(1.1);
  transition: all 0.2s ease;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}

.swoop-highlight:active {
  transform: scale(0.95);
  background: rgba(37, 99, 235, 0.25) !important;
}

/* Improve touch feedback for all interactive elements */
.mobile-button, .mobile-pair, .swoop-highlight {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Ensure minimum touch target size (44px recommended by Apple) */
.mobile-pair {
  min-height: 44px;
}

.mobile-button {
  min-height: 48px;
}

.swoop-highlight {
  min-height: 20px;
  min-width: 20px;
}

/* Responsive grid cells */
@media screen and (max-width: 896px) and (orientation: landscape) {
  .mobile-grid {
    font-size: 0.7rem;
  }

  .swoop-tile {
    min-height: 16px;
    min-width: 16px;
  }

  .swoop-piece {
    font-size: 0.8rem;
  }

  .swoop-piece.active {
    font-size: 1rem;
  }
}

@media screen and (max-width: 736px) and (orientation: landscape) {
  .mobile-grid {
    font-size: 0.6rem;
    gap: 0.5px;
  }

  .swoop-tile {
    min-height: 14px;
    min-width: 14px;
  }

  .swoop-piece {
    font-size: 0.7rem;
  }

  .swoop-piece.active {
    font-size: 0.9rem;
  }
}

.swoop-die {
  width: 34px;
  height: 34px;
  border-radius: 8px;
  background: #111827;
  color: #fff;
  display: grid;
  place-items: center;
  font-weight: 800;
}

.swoop-pair {
  border: 1px dashed var(--line);
  border-radius: 10px;
  padding: 6px 10px;
  background: #fff;
  cursor: pointer;
}

.swoop-pair.selected {
  border-color: var(--accent);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.15);
}

.swoop-badge {
  background: #fff;
  border: 1px solid var(--line);
  border-radius: 12px;
  padding: 6px 12px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
  display: flex;
  gap: 8px;
  align-items: center;
}

.swoop-board {
  background: #fff;
  border: 1px solid var(--line);
  border-radius: 14px;
  padding: 16px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.swoop-button {
  border: 0;
  border-radius: 8px;
  padding: 8px 12px;
  background: #e5e7eb;
  color: #111827;
  font-weight: 700;
  cursor: pointer;
}

.swoop-button.primary {
  background: var(--accent);
  color: #fff;
}

.swoop-button.ghost {
  background: #f3f4f6;
}

/* Mobile Toast */
.mobile-toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.9rem;
  z-index: 1000;
  max-width: 80vw;
  text-align: center;
}

/* Mobile Modal */
.mobile-modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  z-index: 1000;
}

.mobile-modal {
  background: white;
  border-radius: 16px;
  padding: 20px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
}

.mobile-modal-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: var(--ink);
}

.mobile-modal-text {
  font-size: 0.9rem;
  color: var(--muted);
  margin: 0 0 16px 0;
}

.mobile-modal-textarea {
  width: 100%;
  min-height: 120px;
  border: 2px solid var(--line);
  border-radius: 8px;
  padding: 12px;
  font-family: monospace;
  font-size: 0.8rem;
  resize: vertical;
  margin-bottom: 16px;
}

.mobile-modal-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mobile-file-input {
  font-size: 0.8rem;
  padding: 8px;
  border: 1px solid var(--line);
  border-radius: 6px;
}

.mobile-modal-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.swoop-button:disabled {
  opacity: 0.45;
  cursor: not-allowed;
}

/* Hide orientation hint in landscape */
@media screen and (orientation: landscape) {
  .orientation-hint {
    display: none !important;
  }
}

/* iPhone-specific optimizations */
/* iPhone SE (1st gen) and similar small screens */
@media screen and (max-width: 568px) and (orientation: landscape) {
  .mobile-header {
    min-height: 50px;
    padding: 6px 10px;
  }

  .mobile-title h1 {
    font-size: 1.2rem;
  }

  .mobile-status {
    font-size: 0.75rem;
    max-width: 150px;
  }

  .mobile-main-layout {
    gap: 6px;
    padding: 6px;
  }

  .mobile-controls-container {
    min-width: 160px;
    max-width: 200px;
  }

  .mobile-button {
    padding: 8px 12px;
    font-size: 0.8rem;
    min-height: 40px;
  }

  .mobile-die {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }
}

/* iPhone 12 mini and similar */
@media screen and (max-width: 812px) and (orientation: landscape) {
  .mobile-grid {
    gap: 1px;
  }

  .mobile-cell {
    min-height: 16px;
    min-width: 16px;
  }
}

/* iPhone 14 Pro Max and similar large screens */
@media screen and (min-width: 932px) and (orientation: landscape) {
  .mobile-header {
    min-height: 70px;
    padding: 12px 16px;
  }

  .mobile-title h1 {
    font-size: 1.8rem;
  }

  .mobile-status {
    font-size: 1rem;
    max-width: 250px;
  }

  .mobile-controls-container {
    min-width: 240px;
    max-width: 300px;
  }

  .mobile-button {
    padding: 16px 20px;
    font-size: 1rem;
    min-height: 56px;
  }

  .mobile-cell {
    min-height: 24px;
    min-width: 24px;
    font-size: 0.8rem;
  }

  .swoop-piece {
    font-size: 1.2rem;
  }

  .swoop-piece.active {
    font-size: 1.4rem;
  }
}

/* Prevent zoom on input focus */
input, textarea, select {
  font-size: 16px !important;
}

/* Smooth scrolling for modal */
.mobile-modal {
  -webkit-overflow-scrolling: touch;
}

/* Performance optimizations */
.mobile-game-container * {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .swoop-ring {
    animation: none;
  }

  .mobile-button, .mobile-pair, .swoop-highlight {
    transition: none;
  }
}
